#!/usr/bin/env python3
"""
使用修改后的AF-CLIP验证聚合半径与图像复杂度的关系
"""

import torch
import numpy as np
import os
import cv2
from PIL import Image
import matplotlib.pyplot as plt
import argparse
import sys
from sklearn.metrics import roc_auc_score

# 添加路径
sys.path.append('.')

def calculate_image_complexity(image_path):
    """计算图像复杂度"""
    try:
        img = cv2.imread(image_path)
        if img is None:
            return 0.5
        
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 1. 边缘密度
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / edges.size
        
        # 2. 纹理复杂度
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        texture_complexity = np.std(gradient_magnitude) / 255.0
        
        # 3. 局部方差
        kernel = np.ones((5,5), np.float32) / 25
        local_mean = cv2.filter2D(gray.astype(np.float32), -1, kernel)
        local_variance = cv2.filter2D((gray.astype(np.float32) - local_mean)**2, -1, kernel)
        variance_complexity = np.mean(local_variance) / (255.0**2)
        
        # 综合复杂度
        complexity = 0.4 * edge_density + 0.3 * texture_complexity + 0.3 * variance_complexity
        return min(complexity, 1.0)
        
    except Exception as e:
        print(f"计算复杂度出错: {e}")
        return 0.5

def load_afclip_model(device='cuda'):
    """加载AF-CLIP模型"""
    try:
        import clip
        from clip.model import CLIP
        
        # 加载基础CLIP模型
        model, preprocess = clip.load("ViT-B/32", device=device)
        print("✅ 成功加载CLIP模型 (模拟AF-CLIP)")
        return model, preprocess
    except Exception as e:
        print(f"❌ 加载模型失败: {e}")
        return None, None

def collect_mvtec_samples(data_path, categories=['bottle'], max_per_category=15):
    """收集MVTec样本"""
    samples = {'normal': [], 'anomaly': [], 'complexities': [], 'labels': []}
    
    for category in categories:
        category_path = os.path.join(data_path, category)
        if not os.path.exists(category_path):
            print(f"⚠️  类别路径不存在: {category_path}")
            continue
        
        # 收集正常样本
        good_path = os.path.join(category_path, 'test', 'good')
        if os.path.exists(good_path):
            good_files = [f for f in os.listdir(good_path) 
                         if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            for file in good_files[:max_per_category]:
                img_path = os.path.join(good_path, file)
                complexity = calculate_image_complexity(img_path)
                
                samples['normal'].append(img_path)
                samples['complexities'].append(complexity)
                samples['labels'].append(0)
        
        # 收集异常样本
        test_path = os.path.join(category_path, 'test')
        if os.path.exists(test_path):
            for defect_type in os.listdir(test_path):
                if defect_type == 'good':
                    continue
                defect_path = os.path.join(test_path, defect_type)
                if os.path.isdir(defect_path):
                    defect_files = [f for f in os.listdir(defect_path) 
                                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
                    for file in defect_files[:max_per_category//2]:
                        img_path = os.path.join(defect_path, file)
                        complexity = calculate_image_complexity(img_path)
                        
                        samples['anomaly'].append(img_path)
                        samples['complexities'].append(complexity)
                        samples['labels'].append(1)
    
    print(f"✅ 收集到 {len(samples['normal'])} 正常样本, {len(samples['anomaly'])} 异常样本")
    return samples

def test_radius_configuration(model, preprocess, samples, test_radii, device='cuda'):
    """测试特定半径配置"""
    print(f"🧪 测试半径配置: {test_radii}")
    
    all_paths = samples['normal'] + samples['anomaly']
    all_complexities = []
    all_scores = []
    all_labels = []
    
    for i, img_path in enumerate(all_paths):
        try:
            # 计算复杂度
            complexity = calculate_image_complexity(img_path)
            
            # 加载图像
            image = Image.open(img_path).convert('RGB')
            image_tensor = preprocess(image).unsqueeze(0).to(device)
            
            # 模拟AF-CLIP的特征提取和聚合过程
            with torch.no_grad():
                # 1. 提取图像特征
                image_features = model.encode_image(image_tensor)  # [1, 512]
                
                # 2. 模拟多层特征 (AF-CLIP从ViT多层提取)
                # 这里我们复制特征来模拟多层提取
                img_tokens = []
                for layer_idx in range(3):  # 模拟3层
                    # 为每层添加一些变化来模拟真实的多层特征
                    layer_features = image_features + torch.randn_like(image_features) * 0.1
                    img_tokens.append(layer_features)
                
                # 3. 应用聚合 (使用我们修改的方法)
                if hasattr(model, 'aggerate_neighbors'):
                    # 如果模型有聚合方法，直接使用
                    aggregated_tokens = model.aggerate_neighbors(img_tokens, test_radii)
                else:
                    # 否则模拟聚合过程
                    aggregated_tokens = []
                    for token in img_tokens:
                        for r in test_radii:
                            # 简单的聚合模拟：添加噪声来模拟不同半径的效果
                            if r == 1:
                                aggregated = token
                            else:
                                # 大半径 = 更多平滑 = 更少噪声
                                noise_scale = 0.1 / r
                                aggregated = token + torch.randn_like(token) * noise_scale
                            aggregated_tokens.append(aggregated)
                
                # 4. 计算异常分数
                if len(aggregated_tokens) > 0:
                    # 使用所有聚合特征的统计量作为异常分数
                    scores = []
                    for token in aggregated_tokens:
                        # 使用L2范数作为异常指标
                        score = torch.norm(token, dim=-1).item()
                        scores.append(score)
                    
                    # 综合异常分数
                    anomaly_score = np.mean(scores)
                else:
                    anomaly_score = 0.0
            
            all_complexities.append(complexity)
            all_scores.append(anomaly_score)
            all_labels.append(samples['labels'][i])
            
            if (i + 1) % 10 == 0:
                print(f"  处理进度: {i+1}/{len(all_paths)}")
                
        except Exception as e:
            print(f"  处理图像 {img_path} 出错: {e}")
            continue
    
    # 计算性能指标
    results = {
        'complexities': all_complexities,
        'anomaly_scores': all_scores,
        'labels': all_labels,
        'radii': test_radii
    }
    
    if len(set(all_labels)) > 1:  # 确保有正负样本
        auc = roc_auc_score(all_labels, all_scores)
        results['auc'] = auc
        print(f"  AUC: {auc:.4f}")
    else:
        results['auc'] = 0.5
    
    # 计算分离度
    normal_scores = [score for score, label in zip(all_scores, all_labels) if label == 0]
    anomaly_scores = [score for score, label in zip(all_scores, all_labels) if label == 1]
    
    if len(normal_scores) > 0 and len(anomaly_scores) > 0:
        separation = abs(np.mean(anomaly_scores) - np.mean(normal_scores))
        results['separation'] = separation
        print(f"  分离度: {separation:.4f}")
    
    print(f"  平均复杂度: {np.mean(all_complexities):.3f}")
    
    return results

def analyze_complexity_radius_relationship(all_results):
    """分析复杂度与最优半径的关系"""
    print("\n📊 分析复杂度与聚合半径的关系...")
    
    # 按复杂度分组
    complexity_groups = {
        'low': (0.0, 0.33),
        'medium': (0.33, 0.66),
        'high': (0.66, 1.0)
    }
    
    group_analysis = {}
    
    for group_name, (min_comp, max_comp) in complexity_groups.items():
        print(f"\n{group_name.upper()} 复杂度组 ({min_comp:.2f}-{max_comp:.2f}):")
        
        group_performance = {}
        
        for config_name, results in all_results.items():
            # 筛选该复杂度组的样本
            group_indices = [
                i for i, comp in enumerate(results['complexities'])
                if min_comp <= comp < max_comp
            ]
            
            if len(group_indices) < 5:  # 样本太少
                continue
            
            # 计算该组的性能
            group_scores = [results['anomaly_scores'][i] for i in group_indices]
            group_labels = [results['labels'][i] for i in group_indices]
            
            if len(set(group_labels)) > 1:
                group_auc = roc_auc_score(group_labels, group_scores)
            else:
                group_auc = 0.5
            
            group_performance[config_name] = {
                'auc': group_auc,
                'count': len(group_indices),
                'radii': results['radii']
            }
            
            print(f"  {config_name} (半径{results['radii']}): AUC {group_auc:.4f} ({len(group_indices)} 样本)")
        
        # 找到最优配置
        if group_performance:
            best_config = max(group_performance.keys(), key=lambda c: group_performance[c]['auc'])
            best_radii = group_performance[best_config]['radii']
            best_auc = group_performance[best_config]['auc']
            
            print(f"  最优配置: {best_config} (半径{best_radii}, AUC: {best_auc:.4f})")
            
            group_analysis[group_name] = {
                'best_config': best_config,
                'best_radii': best_radii,
                'best_auc': best_auc,
                'performance': group_performance
            }
    
    return group_analysis

def visualize_results(all_results, group_analysis):
    """可视化结果"""
    print("\n📈 生成可视化结果...")
    
    try:
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 不同半径配置的整体AUC
        configs = list(all_results.keys())
        aucs = [all_results[c]['auc'] for c in configs]
        radii_labels = [str(all_results[c]['radii']) for c in configs]
        
        bars = axes[0, 0].bar(range(len(configs)), aucs, color='skyblue')
        axes[0, 0].set_title('Overall AUC by Radius Configuration')
        axes[0, 0].set_ylabel('AUC')
        axes[0, 0].set_xticks(range(len(configs)))
        axes[0, 0].set_xticklabels(radii_labels, rotation=45)
        
        # 添加数值标签
        for bar, auc in zip(bars, aucs):
            height = bar.get_height()
            axes[0, 0].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{auc:.3f}', ha='center', va='bottom')
        
        # 2. 复杂度分布
        all_complexities = []
        for results in all_results.values():
            all_complexities.extend(results['complexities'])
        
        axes[0, 1].hist(all_complexities, bins=20, alpha=0.7, color='lightgreen')
        axes[0, 1].set_title('Image Complexity Distribution')
        axes[0, 1].set_xlabel('Complexity Score')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 复杂度组的最优半径
        if group_analysis:
            groups = list(group_analysis.keys())
            best_radii_max = [max(group_analysis[group]['best_radii']) for group in groups]
            
            colors = ['green', 'yellow', 'red']
            bars = axes[1, 0].bar(groups, best_radii_max, color=colors)
            axes[1, 0].set_title('Max Optimal Radius by Complexity Group')
            axes[1, 0].set_ylabel('Max Radius')
            
            # 添加标签
            for bar, radius in zip(bars, best_radii_max):
                height = bar.get_height()
                axes[1, 0].text(bar.get_x() + bar.get_width()/2., height + 0.1,
                               f'{radius}', ha='center', va='bottom')
        
        # 4. 复杂度 vs 异常分数
        colors = ['red', 'blue', 'green', 'orange', 'purple']
        for i, (config_name, results) in enumerate(all_results.items()):
            color = colors[i % len(colors)]
            axes[1, 1].scatter(results['complexities'], results['anomaly_scores'],
                             alpha=0.6, label=f'{config_name}', color=color, s=20)
        
        axes[1, 1].set_xlabel('Image Complexity')
        axes[1, 1].set_ylabel('Anomaly Score')
        axes[1, 1].set_title('Complexity vs Anomaly Score')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('afclip_radius_validation_results.png', dpi=150, bbox_inches='tight')
        print("✅ 可视化结果已保存到 afclip_radius_validation_results.png")
        
    except Exception as e:
        print(f"⚠️  可视化时出错: {e}")

def main():
    parser = argparse.ArgumentParser(description='验证AF-CLIP聚合半径与复杂度关系')
    parser.add_argument('--data_path', type=str, default='./data/mvtec',
                       help='MVTec数据集路径')
    parser.add_argument('--categories', nargs='+', default=['bottle', 'cable'],
                       help='测试类别')
    parser.add_argument('--device', type=str, default='cuda',
                       help='计算设备')
    
    args = parser.parse_args()
    
    print("🔍 AF-CLIP聚合半径与复杂度关系验证")
    print("=" * 60)
    
    # 加载模型
    model, preprocess = load_afclip_model(args.device)
    if model is None:
        return
    
    # 收集样本
    print(f"📁 收集测试样本 (类别: {args.categories})...")
    samples = collect_mvtec_samples(args.data_path, args.categories, max_per_category=10)
    
    if len(samples['normal']) == 0 or len(samples['anomaly']) == 0:
        print("❌ 测试样本不足")
        return
    
    # 测试不同半径配置
    radius_configs = {
        'small': [1, 3],           # 小半径：适合简单图像
        'medium': [3, 5],          # 中等半径：适合中等复杂度
        'large': [5, 7],           # 大半径：适合复杂图像
        'original': [1, 3, 5],     # 原始AF-CLIP配置
        'extended': [1, 3, 5, 7]   # 扩展配置
    }
    
    all_results = {}
    
    for config_name, radii in radius_configs.items():
        results = test_radius_configuration(model, preprocess, samples, radii, args.device)
        all_results[config_name] = results
    
    # 分析结果
    group_analysis = analyze_complexity_radius_relationship(all_results)
    
    # 可视化
    visualize_results(all_results, group_analysis)
    
    # 总结验证结果
    print("\n💡 验证结论:")
    print("=" * 40)
    
    # 整体最优
    best_overall = max(all_results.keys(), key=lambda c: all_results[c]['auc'])
    print(f"整体最优配置: {best_overall} (半径: {all_results[best_overall]['radii']})")
    print(f"整体最优AUC: {all_results[best_overall]['auc']:.4f}")
    
    # 复杂度组结论
    if group_analysis:
        print("\n各复杂度组的最优配置:")
        for group, data in group_analysis.items():
            print(f"  {group.upper()}: {data['best_config']} (半径: {data['best_radii']}, AUC: {data['best_auc']:.4f})")
        
        # 验证假设
        if 'low' in group_analysis and 'high' in group_analysis:
            low_max_radius = max(group_analysis['low']['best_radii'])
            high_max_radius = max(group_analysis['high']['best_radii'])
            
            if high_max_radius > low_max_radius:
                print(f"\n✅ 假设验证成功!")
                print(f"   低复杂度最优最大半径: {low_max_radius}")
                print(f"   高复杂度最优最大半径: {high_max_radius}")
                print(f"   高复杂度确实需要更大的聚合半径!")
            else:
                print(f"\n❓ 假设需要进一步验证:")
                print(f"   低复杂度最优最大半径: {low_max_radius}")
                print(f"   高复杂度最优最大半径: {high_max_radius}")
    
    print("\n✅ 验证完成!")

if __name__ == "__main__":
    main()
