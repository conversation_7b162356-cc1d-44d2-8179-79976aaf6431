#!/usr/bin/env python3
"""
在AF-CLIP框架中验证聚合半径与图像复杂度的关系
使用真实的异常检测数据集验证我们的假设
"""

import torch
import torch.nn.functional as F
import numpy as np
import cv2
import os
import json
from PIL import Image
import matplotlib.pyplot as plt
from sklearn.metrics import roc_auc_score
import argparse

# 导入AF-CLIP相关模块
import clip
from clip.model import CLIP

class ComplexityAnalyzer:
    """简化的图像复杂度分析器"""
    
    def calculate_complexity(self, image_path):
        """计算图像复杂度"""
        # 读取图像
        if isinstance(image_path, str):
            img = cv2.imread(image_path)
            if img is None:
                return 0.5  # 默认中等复杂度
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        else:
            img = image_path
        
        gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
        
        # 1. 边缘密度
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / edges.size
        
        # 2. 纹理复杂度 (简化版LBP)
        h, w = gray.shape
        texture_variance = 0
        for i in range(1, h-1, 5):  # 采样计算以提高速度
            for j in range(1, w-1, 5):
                neighborhood = gray[i-1:i+2, j-1:j+2]
                texture_variance += np.var(neighborhood)
        texture_complexity = min(texture_variance / ((h//5) * (w//5) * 255**2), 1.0)
        
        # 3. 空间频率
        grad_x = np.diff(gray, axis=1)
        grad_y = np.diff(gray, axis=0)
        spatial_freq = np.sqrt(np.mean(grad_x**2) + np.mean(grad_y**2))
        spatial_freq = min(spatial_freq / 100.0, 1.0)  # 归一化
        
        # 综合复杂度
        complexity = 0.4 * edge_density + 0.3 * texture_complexity + 0.3 * spatial_freq
        return complexity

class AFCLIPRadiusValidator:
    """AF-CLIP聚合半径验证器"""
    
    def __init__(self, model_path=None, device='cuda'):
        self.device = device
        self.complexity_analyzer = ComplexityAnalyzer()
        
        # 加载CLIP模型
        self.model, self.preprocess = clip.load("ViT-B/32", device=device)
        
        # 模拟AF-CLIP的聚合功能
        self.gaussian_kernels = {}
        for size in [3, 5, 7, 9, 11]:
            kernel = self._create_gaussian_kernel(size)
            self.gaussian_kernels[size] = kernel.to(device)
    
    def _create_gaussian_kernel(self, size, sigma=None):
        """创建高斯核"""
        if sigma is None:
            sigma = size / 3.0
        
        x = torch.arange(size, dtype=torch.float32) - (size - 1) / 2
        y = torch.arange(size, dtype=torch.float32) - (size - 1) / 2
        x, y = torch.meshgrid(x, y, indexing='ij')
        
        kernel = torch.exp(-(x**2 + y**2) / (2 * sigma**2))
        kernel = kernel / kernel.sum()
        return kernel
    
    def aggregate_features(self, features, radius):
        """
        模拟AF-CLIP的聚合功能
        Args:
            features: [B, N, D] patch特征 (N = H*W + 1, 包含class token)
            radius: 聚合半径
        """
        if radius == 1:
            return features
        
        B, N, D = features.shape
        
        # 分离class token和patch特征
        cls_token = features[:, :1, :]  # [B, 1, D]
        patch_features = features[:, 1:, :]  # [B, H*W, D]
        
        # 计算空间尺寸
        patch_num = N - 1
        H = W = int(patch_num ** 0.5)
        
        if H * W != patch_num:
            # 如果不是完美的正方形，直接返回
            return features
        
        # 重塑为空间维度
        patch_2d = patch_features.view(B, H, W, D).permute(0, 3, 1, 2)  # [B, D, H, W]
        
        # 选择合适的核大小
        kernel_size = min(radius * 2 + 1, min(H, W))
        if kernel_size % 2 == 0:
            kernel_size += 1
        
        if kernel_size not in self.gaussian_kernels:
            kernel = self._create_gaussian_kernel(kernel_size).to(self.device)
            self.gaussian_kernels[kernel_size] = kernel
        else:
            kernel = self.gaussian_kernels[kernel_size]
        
        # 执行聚合 (使用unfold模拟)
        padding = kernel_size // 2
        unfolded = F.unfold(patch_2d, kernel_size, padding=padding, stride=1)
        # [B, D*K*K, H*W]
        
        unfolded = unfolded.view(B, D, kernel_size*kernel_size, H*W)
        # [B, D, K*K, H*W]
        
        # 应用高斯权重
        kernel_weights = kernel.view(1, 1, kernel_size*kernel_size, 1)
        aggregated = torch.sum(unfolded * kernel_weights, dim=2)  # [B, D, H*W]
        
        # 重塑回patch格式
        aggregated_patches = aggregated.permute(0, 2, 1)  # [B, H*W, D]
        
        # 重新组合class token
        aggregated_features = torch.cat([cls_token, aggregated_patches], dim=1)
        
        return aggregated_features
    
    def test_radius_effects(self, image_paths, gt_paths=None, radii=[1, 3, 5, 7, 9]):
        """
        测试不同聚合半径对异常检测效果的影响
        """
        results = {}
        
        for radius in radii:
            print(f"\n测试聚合半径: {radius}")
            
            radius_results = {
                'complexities': [],
                'anomaly_scores': [],
                'gt_labels': [],
                'image_paths': []
            }
            
            for i, img_path in enumerate(image_paths):
                try:
                    # 计算图像复杂度
                    complexity = self.complexity_analyzer.calculate_complexity(img_path)
                    
                    # 加载和预处理图像
                    image = Image.open(img_path).convert('RGB')
                    image_tensor = self.preprocess(image).unsqueeze(0).to(self.device)
                    
                    # 提取特征
                    with torch.no_grad():
                        image_features = self.model.encode_image(image_tensor)
                        
                        # 模拟多层特征提取 (简化版)
                        # 这里我们使用最终的特征，实际AF-CLIP会从多层提取
                        features = image_features.unsqueeze(1)  # [B, 1, D]
                        
                        # 为了模拟patch特征，我们创建一个假的patch维度
                        # 实际应用中应该从ViT的中间层提取patch特征
                        B, _, D = features.shape
                        patch_size = 14  # ViT-B/32的patch数量
                        
                        # 创建模拟的patch特征
                        mock_patches = features.repeat(1, patch_size*patch_size, 1)
                        mock_features = torch.cat([features, mock_patches], dim=1)  # [B, 1+196, D]
                        
                        # 应用聚合
                        aggregated_features = self.aggregate_features(mock_features, radius)
                        
                        # 计算异常分数 (简化版)
                        # 这里使用特征的方差作为异常指标
                        anomaly_score = torch.var(aggregated_features).item()
                    
                    radius_results['complexities'].append(complexity)
                    radius_results['anomaly_scores'].append(anomaly_score)
                    radius_results['image_paths'].append(img_path)
                    
                    # 如果有GT标签
                    if gt_paths and i < len(gt_paths):
                        # 简化：根据文件名判断是否为异常
                        is_anomaly = 'defect' in img_path.lower() or 'anomaly' in img_path.lower()
                        radius_results['gt_labels'].append(1 if is_anomaly else 0)
                    
                    if (i + 1) % 10 == 0:
                        print(f"  处理了 {i+1}/{len(image_paths)} 张图像")
                        
                except Exception as e:
                    print(f"  处理图像 {img_path} 时出错: {e}")
                    continue
            
            results[radius] = radius_results
        
        return results
    
    def analyze_complexity_radius_relationship(self, results):
        """分析复杂度与最优半径的关系"""
        print("\n📊 分析复杂度与聚合半径的关系...")
        
        # 按复杂度分组
        complexity_groups = {
            'low': (0.0, 0.3),
            'medium': (0.3, 0.6),
            'high': (0.6, 1.0)
        }
        
        analysis_results = {}
        
        for group_name, (min_comp, max_comp) in complexity_groups.items():
            print(f"\n{group_name.upper()} 复杂度组 ({min_comp:.1f}-{max_comp:.1f}):")
            
            group_results = {}
            
            for radius in results.keys():
                radius_data = results[radius]
                
                # 筛选该复杂度组的图像
                group_indices = [
                    i for i, comp in enumerate(radius_data['complexities'])
                    if min_comp <= comp < max_comp
                ]
                
                if len(group_indices) == 0:
                    continue
                
                # 计算该组的平均异常分数
                group_scores = [radius_data['anomaly_scores'][i] for i in group_indices]
                avg_score = np.mean(group_scores)
                std_score = np.std(group_scores)
                
                group_results[radius] = {
                    'avg_score': avg_score,
                    'std_score': std_score,
                    'count': len(group_indices)
                }
                
                print(f"  半径 {radius}: 平均分数 {avg_score:.4f} ± {std_score:.4f} ({len(group_indices)} 张图像)")
            
            # 找到最优半径
            if group_results:
                best_radius = max(group_results.keys(), key=lambda r: group_results[r]['avg_score'])
                print(f"  最优半径: {best_radius}")
                
                analysis_results[group_name] = {
                    'best_radius': best_radius,
                    'results': group_results
                }
        
        return analysis_results
    
    def visualize_results(self, results, analysis_results):
        """可视化结果"""
        print("\n📈 生成可视化结果...")
        
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            
            # 1. 复杂度分布
            all_complexities = []
            for radius_data in results.values():
                all_complexities.extend(radius_data['complexities'])
            
            axes[0, 0].hist(all_complexities, bins=20, alpha=0.7, color='skyblue')
            axes[0, 0].set_title('Image Complexity Distribution')
            axes[0, 0].set_xlabel('Complexity Score')
            axes[0, 0].set_ylabel('Frequency')
            axes[0, 0].grid(True, alpha=0.3)
            
            # 2. 不同半径的异常分数分布
            for radius, radius_data in results.items():
                axes[0, 1].hist(radius_data['anomaly_scores'], bins=15, alpha=0.5, 
                              label=f'Radius {radius}')
            
            axes[0, 1].set_title('Anomaly Score Distribution by Radius')
            axes[0, 1].set_xlabel('Anomaly Score')
            axes[0, 1].set_ylabel('Frequency')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 复杂度 vs 异常分数 (不同半径)
            colors = ['red', 'blue', 'green', 'orange', 'purple']
            for i, (radius, radius_data) in enumerate(results.items()):
                color = colors[i % len(colors)]
                axes[1, 0].scatter(radius_data['complexities'], radius_data['anomaly_scores'],
                                 alpha=0.6, label=f'Radius {radius}', color=color, s=20)
            
            axes[1, 0].set_xlabel('Image Complexity')
            axes[1, 0].set_ylabel('Anomaly Score')
            axes[1, 0].set_title('Complexity vs Anomaly Score')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 最优半径 vs 复杂度组
            if analysis_results:
                groups = list(analysis_results.keys())
                best_radii = [analysis_results[group]['best_radius'] for group in groups]
                
                axes[1, 1].bar(groups, best_radii, color=['green', 'yellow', 'red'])
                axes[1, 1].set_title('Optimal Radius by Complexity Group')
                axes[1, 1].set_ylabel('Optimal Radius')
                
                # 添加数值标签
                for i, radius in enumerate(best_radii):
                    axes[1, 1].text(i, radius + 0.1, str(radius), ha='center')
            
            plt.tight_layout()
            plt.savefig('afclip_radius_validation.png', dpi=150, bbox_inches='tight')
            print("✅ 可视化结果已保存到 afclip_radius_validation.png")
            
        except Exception as e:
            print(f"⚠️  可视化时出错: {e}")

def collect_test_images(data_path, max_images=50):
    """收集测试图像"""
    image_paths = []
    
    # 支持的图像格式
    valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
    
    if os.path.isdir(data_path):
        for root, dirs, files in os.walk(data_path):
            for file in files:
                if any(file.lower().endswith(ext) for ext in valid_extensions):
                    image_paths.append(os.path.join(root, file))
                    if len(image_paths) >= max_images:
                        break
            if len(image_paths) >= max_images:
                break
    else:
        print(f"数据路径不存在: {data_path}")
    
    return image_paths[:max_images]

def main():
    parser = argparse.ArgumentParser(description='验证AF-CLIP中聚合半径与复杂度的关系')
    parser.add_argument('--data_path', type=str, default='./data/mvtec', 
                       help='测试数据路径')
    parser.add_argument('--max_images', type=int, default=50,
                       help='最大测试图像数量')
    parser.add_argument('--device', type=str, default='cuda',
                       help='计算设备')
    
    args = parser.parse_args()
    
    print("🔍 AF-CLIP聚合半径验证")
    print("=" * 50)
    
    # 收集测试图像
    print(f"📁 从 {args.data_path} 收集测试图像...")
    image_paths = collect_test_images(args.data_path, args.max_images)
    
    if len(image_paths) == 0:
        print("❌ 未找到测试图像，请检查数据路径")
        return
    
    print(f"✅ 找到 {len(image_paths)} 张测试图像")
    
    # 初始化验证器
    validator = AFCLIPRadiusValidator(device=args.device)
    
    # 测试不同半径的效果
    print("\n🧪 测试不同聚合半径的效果...")
    results = validator.test_radius_effects(image_paths, radii=[1, 3, 5, 7, 9])
    
    # 分析复杂度与半径的关系
    analysis_results = validator.analyze_complexity_radius_relationship(results)
    
    # 可视化结果
    validator.visualize_results(results, analysis_results)
    
    # 总结结论
    print("\n💡 验证结论:")
    if analysis_results:
        for group, data in analysis_results.items():
            print(f"  {group.upper()} 复杂度图像的最优半径: {data['best_radius']}")
    
    print("\n✅ 验证完成！")
    print("📊 请查看生成的可视化图表 afclip_radius_validation.png")

if __name__ == "__main__":
    main()
