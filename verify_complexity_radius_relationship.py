#!/usr/bin/env python3
"""
验证图像复杂度与聚合半径的对应关系
分析不同复杂度的图像应该使用什么样的聚合策略
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import cv2
from sklearn.metrics import mutual_info_score
from scipy import ndimage
import os

class ImageComplexityAnalyzer:
    """图像复杂度分析器"""
    
    def __init__(self):
        self.metrics = {}
    
    def calculate_edge_density(self, image):
        """计算边缘密度 - 反映图像的细节丰富程度"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # Sobel边缘检测
        sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        edge_magnitude = np.sqrt(sobelx**2 + sobely**2)
        
        # 边缘密度 = 边缘像素数 / 总像素数
        edge_density = np.sum(edge_magnitude > edge_magnitude.mean()) / edge_magnitude.size
        return edge_density
    
    def calculate_texture_complexity(self, image):
        """计算纹理复杂度 - 基于局部二值模式(LBP)"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # 简化的LBP计算
        h, w = gray.shape
        lbp = np.zeros_like(gray)
        
        for i in range(1, h-1):
            for j in range(1, w-1):
                center = gray[i, j]
                code = 0
                code |= (gray[i-1, j-1] >= center) << 7
                code |= (gray[i-1, j] >= center) << 6
                code |= (gray[i-1, j+1] >= center) << 5
                code |= (gray[i, j+1] >= center) << 4
                code |= (gray[i+1, j+1] >= center) << 3
                code |= (gray[i+1, j] >= center) << 2
                code |= (gray[i+1, j-1] >= center) << 1
                code |= (gray[i, j-1] >= center) << 0
                lbp[i, j] = code
        
        # 纹理复杂度 = LBP模式的多样性
        unique_patterns = len(np.unique(lbp))
        texture_complexity = unique_patterns / 256.0  # 归一化到0-1
        return texture_complexity
    
    def calculate_spatial_frequency(self, image):
        """计算空间频率 - 反映图像的空间变化程度"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # 计算行方向和列方向的频率
        rf = np.mean(np.diff(gray, axis=1)**2)  # 行频率
        cf = np.mean(np.diff(gray, axis=0)**2)  # 列频率
        
        spatial_freq = np.sqrt(rf + cf)
        return spatial_freq
    
    def calculate_entropy(self, image):
        """计算图像熵 - 反映信息量"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # 计算直方图
        hist, _ = np.histogram(gray, bins=256, range=(0, 256))
        hist = hist / hist.sum()  # 归一化
        
        # 计算熵
        entropy = -np.sum(hist * np.log2(hist + 1e-10))
        return entropy / 8.0  # 归一化到0-1 (8是最大熵)
    
    def analyze_image(self, image):
        """综合分析图像复杂度"""
        metrics = {
            'edge_density': self.calculate_edge_density(image),
            'texture_complexity': self.calculate_texture_complexity(image),
            'spatial_frequency': self.calculate_spatial_frequency(image),
            'entropy': self.calculate_entropy(image)
        }
        
        # 综合复杂度评分 (加权平均)
        weights = {
            'edge_density': 0.3,
            'texture_complexity': 0.3,
            'spatial_frequency': 0.2,
            'entropy': 0.2
        }
        
        # 归一化空间频率
        metrics['spatial_frequency'] = min(metrics['spatial_frequency'] / 1000.0, 1.0)
        
        overall_complexity = sum(weights[k] * metrics[k] for k in weights.keys())
        metrics['overall_complexity'] = overall_complexity
        
        return metrics

def create_synthetic_images():
    """创建不同复杂度的合成图像用于测试"""
    images = {}
    
    # 1. 简单图像 - 纯色块
    simple = np.ones((224, 224, 3), dtype=np.uint8) * 128
    simple[50:150, 50:150] = [255, 0, 0]  # 红色方块
    images['simple'] = simple
    
    # 2. 中等复杂度 - 几何图案
    medium = np.zeros((224, 224, 3), dtype=np.uint8)
    for i in range(0, 224, 20):
        for j in range(0, 224, 20):
            if (i//20 + j//20) % 2 == 0:
                medium[i:i+20, j:j+20] = [255, 255, 255]
    images['medium'] = medium
    
    # 3. 复杂图像 - 随机噪声 + 纹理
    np.random.seed(42)
    complex_img = np.random.randint(0, 256, (224, 224, 3), dtype=np.uint8)
    # 添加一些结构
    for i in range(10):
        center = (np.random.randint(50, 174), np.random.randint(50, 174))
        radius = np.random.randint(10, 30)
        cv2.circle(complex_img, center, radius, 
                  (np.random.randint(0, 256), np.random.randint(0, 256), np.random.randint(0, 256)), -1)
    images['complex'] = complex_img
    
    # 4. 高复杂度 - 细密纹理
    very_complex = np.zeros((224, 224, 3), dtype=np.uint8)
    for i in range(224):
        for j in range(224):
            # 创建复杂的数学纹理
            value = int(128 + 127 * np.sin(i/5) * np.cos(j/5) * np.sin((i+j)/3))
            very_complex[i, j] = [value, value//2, value//3]
    images['very_complex'] = very_complex
    
    return images

def optimal_radius_for_complexity():
    """分析不同复杂度下的最优聚合半径"""
    
    print("🔍 分析复杂度与最优聚合半径的关系...")
    
    # 理论分析
    complexity_radius_theory = {
        'simple': {
            'complexity_range': (0.0, 0.3),
            'optimal_radius': (1, 3),
            'reasoning': '简单图像特征稀疏，小半径聚合即可捕获主要信息'
        },
        'medium': {
            'complexity_range': (0.3, 0.6),
            'optimal_radius': (3, 5),
            'reasoning': '中等复杂度需要中等半径平衡细节和全局信息'
        },
        'complex': {
            'complexity_range': (0.6, 0.8),
            'optimal_radius': (5, 7),
            'reasoning': '复杂图像需要大半径聚合来降噪和提取稳定特征'
        },
        'very_complex': {
            'complexity_range': (0.8, 1.0),
            'optimal_radius': (7, 9),
            'reasoning': '极复杂图像需要强聚合来抑制噪声'
        }
    }
    
    return complexity_radius_theory

def test_aggregation_effects():
    """测试不同聚合半径对不同复杂度图像的效果"""
    
    print("🧪 测试聚合效果...")
    
    # 创建测试图像
    test_images = create_synthetic_images()
    analyzer = ImageComplexityAnalyzer()
    
    results = {}
    
    for img_name, img in test_images.items():
        print(f"\n分析图像: {img_name}")
        
        # 分析复杂度
        complexity_metrics = analyzer.analyze_image(img)
        print(f"  复杂度评分: {complexity_metrics['overall_complexity']:.3f}")
        print(f"  边缘密度: {complexity_metrics['edge_density']:.3f}")
        print(f"  纹理复杂度: {complexity_metrics['texture_complexity']:.3f}")
        
        # 测试不同聚合半径的效果
        radii_effects = {}
        gray_img = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY) if len(img.shape) == 3 else img
        
        for radius in [1, 3, 5, 7, 9]:
            if radius == 1:
                aggregated = gray_img
            else:
                # 模拟聚合效果 (使用高斯滤波)
                kernel_size = radius * 2 + 1
                aggregated = cv2.GaussianBlur(gray_img, (kernel_size, kernel_size), radius/3)
            
            # 计算聚合后的信息保留度
            # 使用结构相似性和边缘保留度
            
            # 边缘保留度
            original_edges = cv2.Canny(gray_img, 50, 150)
            aggregated_edges = cv2.Canny(aggregated.astype(np.uint8), 50, 150)
            edge_preservation = np.sum(original_edges & aggregated_edges) / (np.sum(original_edges) + 1e-10)
            
            # 噪声抑制度 (方差减少)
            noise_reduction = 1 - (np.var(aggregated) / (np.var(gray_img) + 1e-10))
            
            # 综合评分 (平衡信息保留和噪声抑制)
            if complexity_metrics['overall_complexity'] < 0.4:
                # 简单图像：优先保留边缘
                score = 0.7 * edge_preservation + 0.3 * noise_reduction
            else:
                # 复杂图像：优先降噪
                score = 0.4 * edge_preservation + 0.6 * noise_reduction
            
            radii_effects[radius] = {
                'edge_preservation': edge_preservation,
                'noise_reduction': noise_reduction,
                'overall_score': score
            }
        
        # 找到最优半径
        best_radius = max(radii_effects.keys(), key=lambda r: radii_effects[r]['overall_score'])
        
        results[img_name] = {
            'complexity': complexity_metrics,
            'radii_effects': radii_effects,
            'optimal_radius': best_radius
        }
        
        print(f"  最优聚合半径: {best_radius}")
        print(f"  最优评分: {radii_effects[best_radius]['overall_score']:.3f}")
    
    return results

def design_adaptive_mapping():
    """设计自适应的复杂度-半径映射函数"""
    
    print("\n🎯 设计自适应映射函数...")
    
    # 基于实验结果设计映射函数
    def complexity_to_radius(complexity_score, num_scales=5):
        """
        将复杂度评分映射到聚合半径
        
        Args:
            complexity_score: 0-1的复杂度评分
            num_scales: 尺度数量
        
        Returns:
            radii: 每个尺度的半径列表
        """
        
        # 基础半径范围
        min_radius = 1
        max_radius = 9
        
        # 非线性映射：复杂度越高，半径增长越快
        # 使用sigmoid函数的变形
        def sigmoid_mapping(x, steepness=3, shift=0.5):
            return 1 / (1 + np.exp(-steepness * (x - shift)))
        
        # 计算基础半径
        normalized_complexity = sigmoid_mapping(complexity_score)
        base_radius = min_radius + normalized_complexity * (max_radius - min_radius)
        
        # 生成多个尺度的半径
        radii = []
        for i in range(num_scales):
            # 每个尺度在基础半径周围分布
            scale_factor = 0.5 + i * (1.5 / (num_scales - 1))  # 0.5 到 2.0
            radius = base_radius * scale_factor
            radius = max(min_radius, min(radius, max_radius))  # 限制范围
            radii.append(radius)
        
        return radii
    
    # 测试映射函数
    test_complexities = [0.1, 0.3, 0.5, 0.7, 0.9]
    
    print("复杂度 -> 聚合半径映射:")
    for complexity in test_complexities:
        radii = complexity_to_radius(complexity)
        print(f"  复杂度 {complexity:.1f}: 半径 {[f'{r:.1f}' for r in radii]}")
    
    return complexity_to_radius

def visualize_results(results):
    """可视化分析结果"""
    
    print("\n📊 生成可视化结果...")
    
    try:
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 复杂度分布
        complexities = [results[name]['complexity']['overall_complexity'] for name in results.keys()]
        names = list(results.keys())
        
        axes[0, 0].bar(names, complexities, color=['green', 'yellow', 'orange', 'red'])
        axes[0, 0].set_title('Image Complexity Scores')
        axes[0, 0].set_ylabel('Complexity Score')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 2. 最优半径分布
        optimal_radii = [results[name]['optimal_radius'] for name in results.keys()]
        
        axes[0, 1].bar(names, optimal_radii, color=['green', 'yellow', 'orange', 'red'])
        axes[0, 1].set_title('Optimal Aggregation Radius')
        axes[0, 1].set_ylabel('Radius')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. 复杂度 vs 最优半径的关系
        axes[1, 0].scatter(complexities, optimal_radii, s=100, c=['green', 'yellow', 'orange', 'red'])
        for i, name in enumerate(names):
            axes[1, 0].annotate(name, (complexities[i], optimal_radii[i]), 
                              xytext=(5, 5), textcoords='offset points')
        
        # 添加拟合线
        z = np.polyfit(complexities, optimal_radii, 2)
        p = np.poly1d(z)
        x_smooth = np.linspace(min(complexities), max(complexities), 100)
        axes[1, 0].plot(x_smooth, p(x_smooth), 'r--', alpha=0.8)
        
        axes[1, 0].set_xlabel('Complexity Score')
        axes[1, 0].set_ylabel('Optimal Radius')
        axes[1, 0].set_title('Complexity vs Optimal Radius')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 不同半径的效果对比
        radii = [1, 3, 5, 7, 9]
        img_names = list(results.keys())
        
        for i, img_name in enumerate(img_names):
            scores = [results[img_name]['radii_effects'][r]['overall_score'] for r in radii]
            axes[1, 1].plot(radii, scores, 'o-', label=img_name, linewidth=2, markersize=6)
        
        axes[1, 1].set_xlabel('Aggregation Radius')
        axes[1, 1].set_ylabel('Overall Score')
        axes[1, 1].set_title('Radius vs Performance for Different Images')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('complexity_radius_analysis.png', dpi=150, bbox_inches='tight')
        print("✅ 可视化结果已保存到 complexity_radius_analysis.png")
        
    except ImportError:
        print("⚠️  matplotlib未安装，跳过可视化")

def main():
    """主函数"""
    print("🔍 图像复杂度与聚合半径关系验证")
    print("=" * 60)
    
    # 1. 理论分析
    theory = optimal_radius_for_complexity()
    print("\n📚 理论分析:")
    for category, info in theory.items():
        print(f"  {category}: 复杂度 {info['complexity_range']}, "
              f"最优半径 {info['optimal_radius']}")
        print(f"    理由: {info['reasoning']}")
    
    # 2. 实验验证
    results = test_aggregation_effects()
    
    # 3. 设计映射函数
    mapping_func = design_adaptive_mapping()
    
    # 4. 可视化结果
    visualize_results(results)
    
    # 5. 总结建议
    print("\n💡 设计建议:")
    print("  1. 简单图像 (复杂度 < 0.3): 使用小半径 (1-3) 保留细节")
    print("  2. 中等图像 (复杂度 0.3-0.6): 使用中等半径 (3-5) 平衡信息")
    print("  3. 复杂图像 (复杂度 0.6-0.8): 使用大半径 (5-7) 降噪")
    print("  4. 极复杂图像 (复杂度 > 0.8): 使用最大半径 (7-9) 强聚合")
    print("\n  📈 映射函数应该是非线性的，复杂度高时半径增长更快")
    print("  🎯 可以使用sigmoid函数或多项式函数进行映射")

if __name__ == "__main__":
    main()
