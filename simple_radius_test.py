#!/usr/bin/env python3
"""
简化的AF-CLIP聚合半径验证脚本
直接修改现有的aggerate_neighbors方法来测试不同半径的效果
"""

import torch
import numpy as np
import os
import cv2
from PIL import Image
import matplotlib.pyplot as plt
import argparse
import sys

# 添加路径
sys.path.append('.')

def calculate_image_complexity(image_path):
    """计算图像复杂度"""
    try:
        img = cv2.imread(image_path)
        if img is None:
            return 0.5
        
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 边缘密度
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / edges.size
        
        # 纹理复杂度 (梯度方差)
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        texture_complexity = np.std(gradient_magnitude) / 255.0
        
        # 综合复杂度
        complexity = 0.6 * edge_density + 0.4 * texture_complexity
        return min(complexity, 1.0)
        
    except Exception as e:
        print(f"计算复杂度出错: {e}")
        return 0.5

def modify_aggregation_method(model, fixed_radii):
    """修改模型的聚合方法使用固定半径"""
    
    def modified_aggerate_neighbors(img_tokens):
        """修改后的聚合方法"""
        img_token_list = []
        for img_token in img_tokens:
            for r in fixed_radii:  # 使用指定的半径列表
                new_img_token = model.aggerate_neighbor(img_token, r)
                img_token_list.append(new_img_token)
        return img_token_list
    
    # 保存原始方法
    original_method = model.aggerate_neighbors
    
    # 替换方法
    model.aggerate_neighbors = modified_aggerate_neighbors
    
    return original_method

def collect_test_images(data_path, max_per_class=10):
    """收集测试图像"""
    test_images = {'normal': [], 'anomaly': []}
    
    # 遍历所有类别
    for category in os.listdir(data_path):
        category_path = os.path.join(data_path, category)
        if not os.path.isdir(category_path):
            continue
        
        test_path = os.path.join(category_path, 'test')
        if not os.path.exists(test_path):
            continue
        
        # 收集正常图像
        good_path = os.path.join(test_path, 'good')
        if os.path.exists(good_path):
            good_files = [f for f in os.listdir(good_path) 
                         if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            for file in good_files[:max_per_class]:
                test_images['normal'].append(os.path.join(good_path, file))
        
        # 收集异常图像
        for defect_type in os.listdir(test_path):
            if defect_type == 'good':
                continue
            defect_path = os.path.join(test_path, defect_type)
            if os.path.isdir(defect_path):
                defect_files = [f for f in os.listdir(defect_path) 
                               if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
                for file in defect_files[:max_per_class//2]:
                    test_images['anomaly'].append(os.path.join(defect_path, file))
    
    return test_images

def test_single_radius_config(model, preprocess, test_images, radius_config, device='cuda'):
    """测试单个半径配置"""
    print(f"测试半径配置: {radius_config}")
    
    # 修改聚合方法
    original_method = modify_aggregation_method(model, radius_config)
    
    results = {
        'complexities': [],
        'anomaly_scores': [],
        'labels': [],
        'paths': []
    }
    
    try:
        # 处理正常图像
        for img_path in test_images['normal'][:20]:  # 限制数量
            try:
                # 计算复杂度
                complexity = calculate_image_complexity(img_path)
                
                # 加载图像
                image = Image.open(img_path).convert('RGB')
                image_tensor = preprocess(image).unsqueeze(0).to(device)
                
                # 模拟AF-CLIP的检测流程
                with torch.no_grad():
                    # 提取多层特征 (简化版)
                    img_tokens = []
                    
                    # 使用CLIP的图像编码器
                    image_features = model.encode_image(image_tensor)
                    
                    # 模拟多层特征 (实际AF-CLIP会从ViT的多个层提取)
                    # 这里我们复制特征来模拟多层
                    for _ in range(3):  # 模拟3层
                        img_tokens.append(image_features.unsqueeze(1))  # [B, 1, D]
                    
                    # 应用聚合
                    aggregated_tokens = model.aggerate_neighbors(img_tokens)
                    
                    # 计算异常分数 (简化版)
                    if len(aggregated_tokens) > 0:
                        # 使用所有聚合特征的平均范数作为异常分数
                        scores = []
                        for token in aggregated_tokens:
                            score = torch.norm(token, dim=-1).mean().item()
                            scores.append(score)
                        anomaly_score = np.mean(scores)
                    else:
                        anomaly_score = 0.0
                
                results['complexities'].append(complexity)
                results['anomaly_scores'].append(anomaly_score)
                results['labels'].append(0)  # 正常
                results['paths'].append(img_path)
                
            except Exception as e:
                print(f"处理正常图像 {img_path} 出错: {e}")
                continue
        
        # 处理异常图像
        for img_path in test_images['anomaly'][:20]:  # 限制数量
            try:
                complexity = calculate_image_complexity(img_path)
                
                image = Image.open(img_path).convert('RGB')
                image_tensor = preprocess(image).unsqueeze(0).to(device)
                
                with torch.no_grad():
                    img_tokens = []
                    image_features = model.encode_image(image_tensor)
                    
                    for _ in range(3):
                        img_tokens.append(image_features.unsqueeze(1))
                    
                    aggregated_tokens = model.aggerate_neighbors(img_tokens)
                    
                    if len(aggregated_tokens) > 0:
                        scores = []
                        for token in aggregated_tokens:
                            score = torch.norm(token, dim=-1).mean().item()
                            scores.append(score)
                        anomaly_score = np.mean(scores)
                    else:
                        anomaly_score = 0.0
                
                results['complexities'].append(complexity)
                results['anomaly_scores'].append(anomaly_score)
                results['labels'].append(1)  # 异常
                results['paths'].append(img_path)
                
            except Exception as e:
                print(f"处理异常图像 {img_path} 出错: {e}")
                continue
    
    finally:
        # 恢复原始方法
        model.aggerate_neighbors = original_method
    
    # 计算性能指标
    if len(results['labels']) > 0:
        normal_scores = [results['anomaly_scores'][i] for i, label in enumerate(results['labels']) if label == 0]
        anomaly_scores = [results['anomaly_scores'][i] for i, label in enumerate(results['labels']) if label == 1]
        
        if len(normal_scores) > 0 and len(anomaly_scores) > 0:
            # 简单的分离度指标
            separation = abs(np.mean(anomaly_scores) - np.mean(normal_scores))
            results['separation'] = separation
        else:
            results['separation'] = 0.0
        
        print(f"  处理了 {len(results['labels'])} 个样本")
        print(f"  分离度: {results.get('separation', 0):.4f}")
        print(f"  平均复杂度: {np.mean(results['complexities']):.3f}")
    
    return results

def analyze_radius_complexity_relationship(all_results):
    """分析半径与复杂度的关系"""
    print("\n📊 分析半径配置与复杂度的关系...")
    
    # 按复杂度分组
    complexity_groups = {
        'low': (0.0, 0.3),
        'medium': (0.3, 0.6), 
        'high': (0.6, 1.0)
    }
    
    group_analysis = {}
    
    for group_name, (min_comp, max_comp) in complexity_groups.items():
        print(f"\n{group_name.upper()} 复杂度组 ({min_comp:.1f}-{max_comp:.1f}):")
        
        group_performance = {}
        
        for config_name, results in all_results.items():
            # 筛选该复杂度组的样本
            group_indices = [
                i for i, comp in enumerate(results['complexities'])
                if min_comp <= comp < max_comp
            ]
            
            if len(group_indices) < 3:
                continue
            
            # 计算该组的性能
            group_scores = [results['anomaly_scores'][i] for i in group_indices]
            group_labels = [results['labels'][i] for i in group_indices]
            
            # 计算分离度
            normal_scores = [score for score, label in zip(group_scores, group_labels) if label == 0]
            anomaly_scores = [score for score, label in zip(group_scores, group_labels) if label == 1]
            
            if len(normal_scores) > 0 and len(anomaly_scores) > 0:
                separation = abs(np.mean(anomaly_scores) - np.mean(normal_scores))
            else:
                separation = 0.0
            
            group_performance[config_name] = {
                'separation': separation,
                'count': len(group_indices)
            }
            
            print(f"  {config_name}: 分离度 {separation:.4f} ({len(group_indices)} 样本)")
        
        # 找到最优配置
        if group_performance:
            best_config = max(group_performance.keys(), key=lambda c: group_performance[c]['separation'])
            print(f"  最优配置: {best_config}")
            
            group_analysis[group_name] = {
                'best_config': best_config,
                'performance': group_performance
            }
    
    return group_analysis

def main():
    parser = argparse.ArgumentParser(description='简化的AF-CLIP半径验证')
    parser.add_argument('--data_path', type=str, default='./data/mvtec',
                       help='数据路径')
    parser.add_argument('--device', type=str, default='cuda',
                       help='设备')
    
    args = parser.parse_args()
    
    print("🔍 简化的AF-CLIP聚合半径验证")
    print("=" * 50)
    
    # 加载CLIP模型 (作为AF-CLIP的基础)
    try:
        import clip
        model, preprocess = clip.load("ViT-B/32", device=args.device)
        print("✅ 成功加载CLIP模型")
    except Exception as e:
        print(f"❌ 加载模型失败: {e}")
        return
    
    # 收集测试图像
    print("📁 收集测试图像...")
    test_images = collect_test_images(args.data_path, max_per_class=5)
    
    print(f"✅ 收集到 {len(test_images['normal'])} 正常图像, {len(test_images['anomaly'])} 异常图像")
    
    if len(test_images['normal']) == 0 or len(test_images['anomaly']) == 0:
        print("❌ 测试图像不足")
        return
    
    # 测试不同的半径配置
    radius_configs = {
        'small_radius': [1, 3],      # 小半径配置
        'medium_radius': [3, 5],     # 中等半径配置  
        'large_radius': [5, 7],      # 大半径配置
        'original': [1, 3, 5],       # 原始配置
        'extended': [1, 3, 5, 7, 9]  # 扩展配置
    }
    
    all_results = {}
    
    for config_name, radii in radius_configs.items():
        print(f"\n🧪 测试配置: {config_name}")
        results = test_single_radius_config(model, preprocess, test_images, radii, args.device)
        all_results[config_name] = results
    
    # 分析结果
    group_analysis = analyze_radius_complexity_relationship(all_results)
    
    # 简单可视化
    try:
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        
        # 1. 不同配置的整体分离度
        configs = list(all_results.keys())
        separations = [all_results[c].get('separation', 0) for c in configs]
        
        axes[0].bar(configs, separations, color='skyblue')
        axes[0].set_title('Separation by Radius Configuration')
        axes[0].set_ylabel('Separation Score')
        axes[0].tick_params(axis='x', rotation=45)
        
        # 2. 复杂度分布
        all_complexities = []
        for results in all_results.values():
            all_complexities.extend(results['complexities'])
        
        axes[1].hist(all_complexities, bins=15, alpha=0.7, color='lightgreen')
        axes[1].set_title('Image Complexity Distribution')
        axes[1].set_xlabel('Complexity Score')
        axes[1].set_ylabel('Frequency')
        
        plt.tight_layout()
        plt.savefig('simple_radius_test_results.png', dpi=150, bbox_inches='tight')
        print("\n✅ 结果已保存到 simple_radius_test_results.png")
        
    except Exception as e:
        print(f"⚠️  可视化出错: {e}")
    
    # 总结
    print("\n💡 验证结论:")
    print("=" * 30)
    
    # 整体最优配置
    best_overall = max(all_results.keys(), key=lambda c: all_results[c].get('separation', 0))
    print(f"整体最优配置: {best_overall}")
    
    # 复杂度组结论
    if group_analysis:
        for group, data in group_analysis.items():
            print(f"{group.upper()} 复杂度组最优: {data['best_config']}")
    
    print("\n✅ 验证完成!")

if __name__ == "__main__":
    main()
