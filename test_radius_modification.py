#!/usr/bin/env python3
"""
直接修改AF-CLIP的聚合半径来验证效果
测试不同半径对真实异常检测性能的影响
"""

import torch
import numpy as np
import os
import json
from PIL import Image
import matplotlib.pyplot as plt
from sklearn.metrics import roc_auc_score
import argparse
import sys

# 添加当前目录到路径
sys.path.append('.')

# 导入AF-CLIP模块
try:
    import clip
    from clip.model import CLIP
    print("✅ 成功导入CLIP模块")
except ImportError as e:
    print(f"❌ 导入CLIP模块失败: {e}")
    sys.exit(1)

class RadiusTestModifier:
    """AF-CLIP聚合半径测试修改器"""
    
    def __init__(self, model, device='cuda'):
        self.model = model
        self.device = device
        self.original_aggerate_neighbor = model.aggerate_neighbor
        
    def modify_aggregation_radius(self, fixed_radius):
        """修改模型的聚合半径为固定值"""
        def modified_aggerate_neighbor(x, patchsize, stride=1):
            # 强制使用指定的半径
            return self.original_aggerate_neighbor(x, fixed_radius, stride)
        
        # 替换模型的聚合方法
        self.model.aggerate_neighbor = modified_aggerate_neighbor
        
    def restore_original(self):
        """恢复原始的聚合方法"""
        self.model.aggerate_neighbor = self.original_aggerate_neighbor

class ComplexityCalculator:
    """图像复杂度计算器"""
    
    @staticmethod
    def calculate_image_complexity(image_path):
        """计算图像复杂度"""
        try:
            import cv2
            
            # 读取图像
            img = cv2.imread(image_path)
            if img is None:
                return 0.5
            
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 1. 边缘密度
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            
            # 2. 梯度方差 (纹理复杂度)
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
            texture_complexity = np.std(gradient_magnitude) / 255.0
            
            # 3. 局部方差
            kernel = np.ones((5,5), np.float32) / 25
            local_mean = cv2.filter2D(gray.astype(np.float32), -1, kernel)
            local_variance = cv2.filter2D((gray.astype(np.float32) - local_mean)**2, -1, kernel)
            variance_complexity = np.mean(local_variance) / (255.0**2)
            
            # 综合复杂度
            complexity = 0.4 * edge_density + 0.3 * texture_complexity + 0.3 * variance_complexity
            return min(complexity, 1.0)
            
        except Exception as e:
            print(f"计算复杂度时出错: {e}")
            return 0.5

def load_afclip_model(device='cuda'):
    """加载AF-CLIP模型"""
    try:
        # 尝试加载预训练的CLIP模型
        model, preprocess = clip.load("ViT-B/32", device=device)
        print("✅ 成功加载CLIP模型")
        return model, preprocess
    except Exception as e:
        print(f"❌ 加载模型失败: {e}")
        return None, None

def collect_mvtec_samples(data_path, category='bottle', max_samples=20):
    """收集MVTec数据集样本"""
    samples = {'normal': [], 'anomaly': []}
    
    # MVTec数据集结构: data_path/category/train(test)/good(defect_type)/
    category_path = os.path.join(data_path, category)
    
    if not os.path.exists(category_path):
        print(f"❌ 类别路径不存在: {category_path}")
        return samples
    
    # 收集正常样本
    normal_path = os.path.join(category_path, 'test', 'good')
    if os.path.exists(normal_path):
        normal_files = [f for f in os.listdir(normal_path) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        for file in normal_files[:max_samples//2]:
            samples['normal'].append(os.path.join(normal_path, file))
    
    # 收集异常样本
    test_path = os.path.join(category_path, 'test')
    if os.path.exists(test_path):
        for defect_type in os.listdir(test_path):
            if defect_type == 'good':
                continue
            defect_path = os.path.join(test_path, defect_type)
            if os.path.isdir(defect_path):
                defect_files = [f for f in os.listdir(defect_path) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
                for file in defect_files[:max_samples//4]:
                    samples['anomaly'].append(os.path.join(defect_path, file))
    
    print(f"✅ 收集到 {len(samples['normal'])} 个正常样本, {len(samples['anomaly'])} 个异常样本")
    return samples

def test_radius_effect(model, preprocess, samples, radius, device='cuda'):
    """测试特定半径的效果"""
    print(f"\n🧪 测试聚合半径: {radius}")
    
    # 修改模型的聚合半径
    modifier = RadiusTestModifier(model, device)
    modifier.modify_aggregation_radius(radius)
    
    results = {
        'complexities': [],
        'anomaly_scores': [],
        'labels': [],
        'image_paths': []
    }
    
    complexity_calc = ComplexityCalculator()
    
    # 处理正常样本
    for img_path in samples['normal']:
        try:
            # 计算复杂度
            complexity = complexity_calc.calculate_image_complexity(img_path)
            
            # 加载图像
            image = Image.open(img_path).convert('RGB')
            image_tensor = preprocess(image).unsqueeze(0).to(device)
            
            # 提取特征并计算异常分数
            with torch.no_grad():
                # 这里简化处理，实际AF-CLIP会有更复杂的流程
                image_features = model.encode_image(image_tensor)
                
                # 模拟异常分数计算 (使用特征的统计量)
                anomaly_score = torch.norm(image_features, dim=-1).item()
            
            results['complexities'].append(complexity)
            results['anomaly_scores'].append(anomaly_score)
            results['labels'].append(0)  # 正常
            results['image_paths'].append(img_path)
            
        except Exception as e:
            print(f"处理正常样本 {img_path} 时出错: {e}")
            continue
    
    # 处理异常样本
    for img_path in samples['anomaly']:
        try:
            complexity = complexity_calc.calculate_image_complexity(img_path)
            
            image = Image.open(img_path).convert('RGB')
            image_tensor = preprocess(image).unsqueeze(0).to(device)
            
            with torch.no_grad():
                image_features = model.encode_image(image_tensor)
                anomaly_score = torch.norm(image_features, dim=-1).item()
            
            results['complexities'].append(complexity)
            results['anomaly_scores'].append(anomaly_score)
            results['labels'].append(1)  # 异常
            results['image_paths'].append(img_path)
            
        except Exception as e:
            print(f"处理异常样本 {img_path} 时出错: {e}")
            continue
    
    # 恢复原始聚合方法
    modifier.restore_original()
    
    # 计算AUC
    if len(set(results['labels'])) > 1:  # 确保有正负样本
        auc = roc_auc_score(results['labels'], results['anomaly_scores'])
        print(f"  AUC: {auc:.4f}")
        results['auc'] = auc
    else:
        results['auc'] = 0.5
    
    print(f"  处理了 {len(results['labels'])} 个样本")
    print(f"  平均复杂度: {np.mean(results['complexities']):.3f}")
    
    return results

def analyze_complexity_radius_relationship(all_results):
    """分析复杂度与最优半径的关系"""
    print("\n📊 分析复杂度与聚合半径的关系...")
    
    # 按复杂度分组分析
    complexity_groups = {
        'low': (0.0, 0.33),
        'medium': (0.33, 0.66),
        'high': (0.66, 1.0)
    }
    
    group_analysis = {}
    
    for group_name, (min_comp, max_comp) in complexity_groups.items():
        print(f"\n{group_name.upper()} 复杂度组 ({min_comp:.2f}-{max_comp:.2f}):")
        
        group_performance = {}
        
        for radius, results in all_results.items():
            # 筛选该复杂度组的样本
            group_indices = [
                i for i, comp in enumerate(results['complexities'])
                if min_comp <= comp < max_comp
            ]
            
            if len(group_indices) < 5:  # 样本太少
                continue
            
            # 计算该组的性能
            group_scores = [results['anomaly_scores'][i] for i in group_indices]
            group_labels = [results['labels'][i] for i in group_indices]
            
            if len(set(group_labels)) > 1:
                group_auc = roc_auc_score(group_labels, group_scores)
            else:
                group_auc = 0.5
            
            group_performance[radius] = {
                'auc': group_auc,
                'count': len(group_indices),
                'avg_complexity': np.mean([results['complexities'][i] for i in group_indices])
            }
            
            print(f"  半径 {radius}: AUC {group_auc:.4f} ({len(group_indices)} 样本)")
        
        # 找到最优半径
        if group_performance:
            best_radius = max(group_performance.keys(), key=lambda r: group_performance[r]['auc'])
            print(f"  最优半径: {best_radius} (AUC: {group_performance[best_radius]['auc']:.4f})")
            
            group_analysis[group_name] = {
                'best_radius': best_radius,
                'performance': group_performance
            }
    
    return group_analysis

def visualize_results(all_results, group_analysis):
    """可视化结果"""
    print("\n📈 生成可视化结果...")
    
    try:
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 不同半径的整体AUC对比
        radii = list(all_results.keys())
        aucs = [all_results[r]['auc'] for r in radii]
        
        axes[0, 0].bar(radii, aucs, color='skyblue')
        axes[0, 0].set_title('Overall AUC by Aggregation Radius')
        axes[0, 0].set_xlabel('Aggregation Radius')
        axes[0, 0].set_ylabel('AUC')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, auc in enumerate(aucs):
            axes[0, 0].text(radii[i], auc + 0.01, f'{auc:.3f}', ha='center')
        
        # 2. 复杂度分布
        all_complexities = []
        for results in all_results.values():
            all_complexities.extend(results['complexities'])
        
        axes[0, 1].hist(all_complexities, bins=20, alpha=0.7, color='lightgreen')
        axes[0, 1].set_title('Image Complexity Distribution')
        axes[0, 1].set_xlabel('Complexity Score')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 复杂度组的最优半径
        if group_analysis:
            groups = list(group_analysis.keys())
            best_radii = [group_analysis[group]['best_radius'] for group in groups]
            
            colors = ['green', 'yellow', 'red']
            bars = axes[1, 0].bar(groups, best_radii, color=colors)
            axes[1, 0].set_title('Optimal Radius by Complexity Group')
            axes[1, 0].set_ylabel('Optimal Radius')
            
            # 添加数值标签
            for bar, radius in zip(bars, best_radii):
                height = bar.get_height()
                axes[1, 0].text(bar.get_x() + bar.get_width()/2., height + 0.1,
                               f'{radius}', ha='center', va='bottom')
        
        # 4. 复杂度 vs 异常分数散点图
        colors = ['red', 'blue', 'green', 'orange', 'purple']
        for i, (radius, results) in enumerate(all_results.items()):
            color = colors[i % len(colors)]
            axes[1, 1].scatter(results['complexities'], results['anomaly_scores'],
                             alpha=0.6, label=f'Radius {radius}', color=color, s=20)
        
        axes[1, 1].set_xlabel('Image Complexity')
        axes[1, 1].set_ylabel('Anomaly Score')
        axes[1, 1].set_title('Complexity vs Anomaly Score')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('radius_validation_results.png', dpi=150, bbox_inches='tight')
        print("✅ 可视化结果已保存到 radius_validation_results.png")
        
    except Exception as e:
        print(f"⚠️  可视化时出错: {e}")

def main():
    parser = argparse.ArgumentParser(description='验证AF-CLIP聚合半径效果')
    parser.add_argument('--data_path', type=str, default='./data/mvtec',
                       help='MVTec数据集路径')
    parser.add_argument('--category', type=str, default='bottle',
                       help='测试类别')
    parser.add_argument('--max_samples', type=int, default=40,
                       help='最大样本数')
    parser.add_argument('--device', type=str, default='cuda',
                       help='计算设备')
    
    args = parser.parse_args()
    
    print("🔍 AF-CLIP聚合半径效果验证")
    print("=" * 50)
    
    # 加载模型
    print("📦 加载模型...")
    model, preprocess = load_afclip_model(args.device)
    if model is None:
        return
    
    # 收集测试样本
    print(f"📁 收集 {args.category} 类别的测试样本...")
    samples = collect_mvtec_samples(args.data_path, args.category, args.max_samples)
    
    if len(samples['normal']) == 0 and len(samples['anomaly']) == 0:
        print("❌ 未找到测试样本")
        return
    
    # 测试不同半径
    test_radii = [1, 3, 5, 7, 9]
    all_results = {}
    
    for radius in test_radii:
        results = test_radius_effect(model, preprocess, samples, radius, args.device)
        all_results[radius] = results
    
    # 分析结果
    group_analysis = analyze_complexity_radius_relationship(all_results)
    
    # 可视化
    visualize_results(all_results, group_analysis)
    
    # 总结
    print("\n💡 验证结论:")
    print("=" * 30)
    
    # 整体最优半径
    best_overall_radius = max(all_results.keys(), key=lambda r: all_results[r]['auc'])
    print(f"整体最优半径: {best_overall_radius} (AUC: {all_results[best_overall_radius]['auc']:.4f})")
    
    # 复杂度组结论
    if group_analysis:
        for group, data in group_analysis.items():
            print(f"{group.upper()} 复杂度组最优半径: {data['best_radius']}")
    
    # 验证假设
    if group_analysis and len(group_analysis) >= 2:
        low_radius = group_analysis.get('low', {}).get('best_radius', 0)
        high_radius = group_analysis.get('high', {}).get('best_radius', 0)
        
        if high_radius > low_radius:
            print("✅ 验证成功: 高复杂度图像确实需要更大的聚合半径!")
        else:
            print("❌ 假设未得到验证: 需要进一步分析")
    
    print("\n✅ 验证完成!")

if __name__ == "__main__":
    main()
