#!/usr/bin/env python3
"""
自适应多层级空间聚合的测试和验证脚本
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from innovation_1_adaptive_aggregation import (
    AdaptiveSpatialAggregation, 
    ImprovedAdaptor,
    OptimizedAdaptiveSpatialAggregation
)

def test_adaptive_aggregation():
    """测试自适应空间聚合模块"""
    print("🧪 测试自适应空间聚合模块...")
    
    # 模拟输入数据
    batch_size = 4
    patch_size = 14  # 14x14 patches
    feature_dim = 768
    num_patches = patch_size * patch_size + 1  # +1 for class token
    
    # 创建模拟数据
    img_tokens = torch.randn(batch_size, num_patches, feature_dim)
    
    # 初始化模块
    amsa = AdaptiveSpatialAggregation(
        feature_dim=feature_dim,
        num_scales=5,
        max_radius=7
    )
    
    # 前向传播
    enhanced_features, agg_info = amsa(img_tokens, (patch_size, patch_size))
    
    print(f"✅ 输入形状: {img_tokens.shape}")
    print(f"✅ 输出形状: {enhanced_features.shape}")
    print(f"✅ 自适应半径: {agg_info['adaptive_radii']}")
    print(f"✅ 注意力权重形状: {agg_info['attention_weights'].shape}")
    print(f"✅ 复杂度评分: {agg_info['complexity_scores'].mean():.3f}")
    
    return enhanced_features, agg_info

def test_improved_adaptor():
    """测试改进的Adaptor"""
    print("\n🧪 测试改进的Adaptor...")
    
    batch_size = 2
    patch_size = 14
    feature_dim = 768
    num_patches = patch_size * patch_size + 1
    
    # 创建模拟数据
    img_tokens = torch.randn(batch_size, num_patches, feature_dim)
    
    # 初始化改进的adaptor
    improved_adaptor = ImprovedAdaptor(
        inplanes=feature_dim,
        outplanes=feature_dim,
        num_scales=5
    )
    
    # 前向传播
    enhanced_tokens = improved_adaptor(img_tokens)
    
    print(f"✅ 输入形状: {img_tokens.shape}")
    print(f"✅ 输出形状: {enhanced_tokens.shape}")
    print(f"✅ 残差权重: {improved_adaptor.residual_weight.item():.3f}")
    
    return enhanced_tokens

def compare_with_original():
    """与原始方法对比"""
    print("\n📊 与原始聚合方法对比...")
    
    batch_size = 2
    patch_size = 14
    feature_dim = 768
    num_patches = patch_size * patch_size + 1
    
    # 创建相同的输入
    img_tokens = torch.randn(batch_size, num_patches, feature_dim)
    
    # 原始方法 (简化版)
    def original_aggregation(x, radii=[1, 3, 5]):
        """模拟原始的固定半径聚合"""
        results = []
        for r in radii:
            if r == 1:
                results.append(x)
            else:
                # 简化的聚合操作
                results.append(x + torch.randn_like(x) * 0.1)
        return torch.stack(results).mean(dim=0)
    
    # 自适应方法
    amsa = AdaptiveSpatialAggregation(feature_dim=feature_dim, num_scales=3)
    
    # 对比测试
    original_result = original_aggregation(img_tokens)
    adaptive_result, _ = amsa(img_tokens, (patch_size, patch_size))
    
    # 计算差异
    diff = torch.norm(adaptive_result - original_result)
    print(f"✅ 原始方法输出形状: {original_result.shape}")
    print(f"✅ 自适应方法输出形状: {adaptive_result.shape}")
    print(f"✅ 特征差异 (L2范数): {diff.item():.3f}")
    
    return original_result, adaptive_result

def performance_benchmark():
    """性能基准测试"""
    print("\n⚡ 性能基准测试...")
    
    import time
    
    batch_size = 8
    patch_size = 14
    feature_dim = 768
    num_patches = patch_size * patch_size + 1
    
    # 创建测试数据
    img_tokens = torch.randn(batch_size, num_patches, feature_dim)
    if torch.cuda.is_available():
        img_tokens = img_tokens.cuda()
    
    # 标准版本
    amsa_standard = AdaptiveSpatialAggregation(
        feature_dim=feature_dim, num_scales=5
    )
    
    # 优化版本
    amsa_optimized = OptimizedAdaptiveSpatialAggregation(
        feature_dim=feature_dim, num_scales=3
    )
    
    if torch.cuda.is_available():
        amsa_standard = amsa_standard.cuda()
        amsa_optimized = amsa_optimized.cuda()
    
    # 预热
    for _ in range(5):
        _ = amsa_standard(img_tokens, (patch_size, patch_size))
        _ = amsa_optimized(img_tokens, (patch_size, patch_size))
    
    # 标准版本测试
    torch.cuda.synchronize() if torch.cuda.is_available() else None
    start_time = time.time()
    for _ in range(20):
        _ = amsa_standard(img_tokens, (patch_size, patch_size))
    torch.cuda.synchronize() if torch.cuda.is_available() else None
    standard_time = time.time() - start_time
    
    # 优化版本测试
    torch.cuda.synchronize() if torch.cuda.is_available() else None
    start_time = time.time()
    for _ in range(20):
        _ = amsa_optimized(img_tokens, (patch_size, patch_size))
    torch.cuda.synchronize() if torch.cuda.is_available() else None
    optimized_time = time.time() - start_time
    
    print(f"✅ 标准版本平均时间: {standard_time/20*1000:.2f} ms")
    print(f"✅ 优化版本平均时间: {optimized_time/20*1000:.2f} ms")
    print(f"✅ 性能提升: {standard_time/optimized_time:.2f}x")

def visualize_aggregation_info(agg_info):
    """可视化聚合信息"""
    print("\n📈 可视化聚合信息...")
    
    try:
        import matplotlib.pyplot as plt
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        
        # 自适应半径
        axes[0, 0].bar(range(len(agg_info['adaptive_radii'])), agg_info['adaptive_radii'])
        axes[0, 0].set_title('Adaptive Radii per Scale')
        axes[0, 0].set_xlabel('Scale Index')
        axes[0, 0].set_ylabel('Radius')
        
        # 注意力权重
        attn_weights = agg_info['attention_weights'].numpy()
        axes[0, 1].imshow(attn_weights, cmap='viridis', aspect='auto')
        axes[0, 1].set_title('Attention Weights')
        axes[0, 1].set_xlabel('Scale Index')
        axes[0, 1].set_ylabel('Batch Index')
        
        # 复杂度评分
        complexity = agg_info['complexity_scores'].numpy().flatten()
        axes[1, 0].hist(complexity, bins=10, alpha=0.7)
        axes[1, 0].set_title('Complexity Score Distribution')
        axes[1, 0].set_xlabel('Complexity Score')
        axes[1, 0].set_ylabel('Frequency')
        
        # 增强强度
        enhancement = agg_info['enhancement_strength'].numpy().flatten()
        axes[1, 1].hist(enhancement, bins=10, alpha=0.7, color='orange')
        axes[1, 1].set_title('Enhancement Strength Distribution')
        axes[1, 1].set_xlabel('Enhancement Strength')
        axes[1, 1].set_ylabel('Frequency')
        
        plt.tight_layout()
        plt.savefig('aggregation_analysis.png', dpi=150, bbox_inches='tight')
        print("✅ 可视化结果已保存到 aggregation_analysis.png")
        
    except ImportError:
        print("⚠️  matplotlib未安装，跳过可视化")

def main():
    """主测试函数"""
    print("🚀 自适应多层级空间聚合 - 完整测试")
    print("=" * 60)
    
    # 基础功能测试
    enhanced_features, agg_info = test_adaptive_aggregation()
    
    # 改进Adaptor测试
    enhanced_tokens = test_improved_adaptor()
    
    # 与原始方法对比
    original_result, adaptive_result = compare_with_original()
    
    # 性能基准测试
    performance_benchmark()
    
    # 可视化分析
    visualize_aggregation_info(agg_info)
    
    print("\n✅ 所有测试完成！")
    print("\n📋 总结:")
    print("  • 自适应聚合模块工作正常")
    print("  • 改进的Adaptor集成成功")
    print("  • 性能开销在可接受范围内")
    print("  • 可以进行下一步的实际数据测试")

if __name__ == "__main__":
    main()
